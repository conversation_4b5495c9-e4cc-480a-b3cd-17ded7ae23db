import type { DefaultValue } from '@milkdown/kit/core';
import {
  Editor,
  EditorStatus,
  defaultValueCtx,
  editorViewOptionsCtx,
  rootCtx,
} from '@milkdown/kit/core';
import { $remark, getMarkdown } from '@milkdown/kit/utils';
import { gfm } from '@milkdown/kit/preset/gfm';
import { history } from '@milkdown/kit/plugin/history';
import { indent, indentConfig } from '@milkdown/kit/plugin/indent';
import { clipboard } from '@milkdown/kit/plugin/clipboard';
import { trailing } from '@milkdown/kit/plugin/trailing';
import type { ListenerManager } from '@milkdown/kit/plugin/listener';
import { listener, listenerCtx } from '@milkdown/kit/plugin/listener';
import {
  codeBlockAttr as codeBlockAttrOrigin,
  codeBlockSchema as codeBlockSchemaOrigin,
  commonmark,
  htmlSchema,
} from '@milkdown/kit/preset/commonmark';
import { mentionSchema } from '@/components/MdEditor/plugin-mention/MentionShecma';
import { linkSchema } from '@/components/MdEditor/plugin-link';
import type { CrepeFeatureConfig } from '../feature';
import { CrepeFeature, defaultFeatures, loadFeature } from '../feature';
import { configureFeatures, crepeCtx } from './slice';
import { codeBlockAttr, codeBlockSchema } from '../schema/code-block';

export interface CrepeConfig {
  features?: Partial<Record<CrepeFeature, boolean>>;
  featureConfigs?: CrepeFeatureConfig;
  root?: Node | string | null;
  defaultValue?: DefaultValue;
}
// 创建一个自定义的 remark 插件来移除自动链接节点
const removeAutolinkPlugin = $remark('removeAutolink', () => {
  return (tree: any) => {
    // 遍历语法树，移除 autolink 类型的节点
    function visit(node: any, parent?: any, index?: number) {
      if (
        node.type === 'link' &&
        node.url &&
        !node.title &&
        node.children?.length === 1 &&
        node.children[0].value === node.url
      ) {
        // 这是一个自动链接，将其替换为纯文本
        if (parent && typeof index === 'number') {
          parent.children[index] = {
            type: 'text',
            value: node.url,
          };
        }
        return;
      }

      if (node.children) {
        for (let i = 0; i < node.children.length; i++) {
          visit(node.children[i], node, i);
        }
      }
    }

    visit(tree);
    return tree;
  };
});

export class Crepe {
  static Feature = CrepeFeature;

  readonly #editor: Editor;

  readonly #initPromise: Promise<unknown>;

  readonly #rootElement: Node;

  #editable = true;

  constructor({
    root,
    features = {},
    featureConfigs = {},
    defaultValue = '',
  }: CrepeConfig) {
    const enabledFeatures = Object.entries({
      ...defaultFeatures,
      ...features,
    })
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature as CrepeFeature);

    this.#rootElement =
      (typeof root === 'string' ? document.querySelector(root) : root) ??
      document.body;

    this.#editor = Editor.make()
      .config((ctx) => {
        ctx.inject(crepeCtx, this);
      })
      .config(configureFeatures(enabledFeatures))
      .config((ctx) => {
        ctx.set(rootCtx, this.#rootElement);
        ctx.set(defaultValueCtx, defaultValue);
        ctx.set(editorViewOptionsCtx, {
          editable: () => this.#editable,
        });
        ctx.update(indentConfig.key, (value) => ({
          ...value,
          size: 4,
        }));
      })
      .use(commonmark)
      .use(listener)
      .use(history)
      .use(indent)
      .use(trailing)
      .use(clipboard)
      .use(gfm) // 启用GFM以支持表格等功能
      .use(removeAutolinkPlugin)
      .use(linkSchema)
      .use(mentionSchema);

    this.#editor.remove(trailing);
    // this.#editor.remove(htmlSchema);
    this.#editor.remove(codeBlockAttrOrigin);
    this.#editor.remove(codeBlockSchemaOrigin);

    this.#editor.use(codeBlockAttr).use(codeBlockSchema);

    const promiseList: Promise<unknown>[] = [];

    enabledFeatures.forEach((feature) => {
      const config = (featureConfigs as Partial<Record<CrepeFeature, never>>)[
        feature
      ];
      promiseList.push(loadFeature(feature, this.#editor, config));
    });

    this.#initPromise = Promise.all(promiseList);
  }

  async create() {
    await this.#initPromise;
    return this.#editor.create();
  }

  async destroy() {
    await this.#initPromise;
    return this.#editor.destroy();
  }

  get editor(): Editor {
    return this.#editor;
  }

  setReadonly(value: boolean) {
    this.#editable = !value;
    return this;
  }

  getMarkdown() {
    return this.#editor.action(getMarkdown());
  }

  on(fn: (api: ListenerManager) => void) {
    if (this.#editor.status !== EditorStatus.Created) {
      this.#editor.config((ctx) => {
        const listener = ctx.get(listenerCtx);
        fn(listener);
      });
      return this;
    }
    this.#editor.action((ctx) => {
      const listener = ctx.get(listenerCtx);
      fn(listener);
    });
    return this;
  }
}
